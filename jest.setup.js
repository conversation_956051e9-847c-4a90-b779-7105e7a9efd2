// Jest setup file for SDK integration tests

// Mock fetch globally for tests
global.fetch = require('node-fetch');

// Mock console methods to reduce noise in tests
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeAll(() => {
  console.error = jest.fn();
  console.warn = jest.fn();
});

afterAll(() => {
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});

// Global test timeout
jest.setTimeout(30000);

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.NEXT_PUBLIC_BACKEND_URL = 'http://localhost:8400';
