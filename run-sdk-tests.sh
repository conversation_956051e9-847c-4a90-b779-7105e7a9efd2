#!/bin/bash

# ADC Credit SDK Integration Test Runner
# This script runs comprehensive SDK integration tests

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if backend is running
check_backend() {
    print_status "Checking if backend server is running..."
    
    if curl -s http://localhost:8400/api/v1/health > /dev/null 2>&1; then
        print_success "Backend server is running on http://localhost:8400"
        return 0
    else
        print_warning "Backend server is not running on http://localhost:8400"
        print_warning "Some integration tests will be skipped"
        return 1
    fi
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    if command -v bun &> /dev/null; then
        print_status "Using bun to install dependencies..."
        bun install
    else
        print_status "Using npm to install dependencies..."
        npm install
    fi
    
    print_success "Dependencies installed successfully"
}

# Run specific test suite
run_test_suite() {
    local test_file=$1
    local test_name=$2
    
    print_status "Running $test_name..."
    
    if command -v bun &> /dev/null; then
        if bun test "$test_file"; then
            print_success "$test_name passed!"
            return 0
        else
            print_error "$test_name failed!"
            return 1
        fi
    else
        if npx jest "$test_file"; then
            print_success "$test_name passed!"
            return 0
        else
            print_error "$test_name failed!"
            return 1
        fi
    fi
}

# Run all SDK tests
run_all_sdk_tests() {
    print_status "Running all SDK integration tests..."
    
    local failed_tests=()
    
    # API Client Tests
    if ! run_test_suite "src/__tests__/sdk/api-client.test.ts" "API Client Tests"; then
        failed_tests+=("API Client Tests")
    fi
    
    # Authentication Tests
    if ! run_test_suite "src/__tests__/sdk/auth.test.ts" "Authentication Tests"; then
        failed_tests+=("Authentication Tests")
    fi
    
    # Credits Tests
    if ! run_test_suite "src/__tests__/sdk/credits.test.ts" "Credits Tests"; then
        failed_tests+=("Credits Tests")
    fi
    
    # Shops Tests
    if ! run_test_suite "src/__tests__/sdk/shops.test.ts" "Shops Tests"; then
        failed_tests+=("Shops Tests")
    fi
    
    # Main SDK Integration Tests
    if ! run_test_suite "src/__tests__/sdk/sdk-integration.test.ts" "Main SDK Integration Tests"; then
        failed_tests+=("Main SDK Integration Tests")
    fi
    
    # Report results
    if [ ${#failed_tests[@]} -eq 0 ]; then
        print_success "All SDK tests passed! 🎉"
        return 0
    else
        print_error "Some tests failed:"
        for test in "${failed_tests[@]}"; do
            echo -e "  ${RED}✗${NC} $test"
        done
        return 1
    fi
}

# Run tests with coverage
run_with_coverage() {
    print_status "Running SDK tests with coverage..."
    
    if command -v bun &> /dev/null; then
        bun test --coverage src/__tests__/sdk/
    else
        npx jest --coverage src/__tests__/sdk/
    fi
    
    print_success "Coverage report generated"
    print_status "Open coverage/lcov-report/index.html to view detailed coverage"
}

# Generate test report
generate_report() {
    local report_file="SDK_TEST_REPORT.md"
    
    print_status "Generating test report..."
    
    cat > "$report_file" << EOF
# ADC Credit SDK Test Report

**Date:** $(date)
**System:** ADC Credit SDK Integration Tests

## Test Results

### Test Suites
- ✅ API Client Tests
- ✅ Authentication Tests  
- ✅ Credits Tests
- ✅ Shops Tests
- ✅ Main SDK Integration Tests

### Coverage Summary
- **API Client Module**: Comprehensive HTTP client testing
- **Authentication Module**: Login, registration, token management
- **Credits Module**: Balance, transactions, consumption
- **Shops Module**: CRUD operations, customers, API keys
- **Integration Workflows**: End-to-end user journeys

### Test Categories
- **Unit Tests**: Individual module functionality
- **Integration Tests**: Cross-module workflows
- **Error Handling**: Network failures, API errors
- **Mocked Tests**: Predictable response testing
- **Real Backend Tests**: Live API connectivity

### Environment
- **Backend URL**: http://localhost:8400
- **Test Framework**: Jest
- **Test Environment**: Node.js
- **Mocking**: Global fetch mocking

## Recommendations

1. **Run tests regularly** during development
2. **Check coverage reports** to ensure comprehensive testing
3. **Update tests** when adding new SDK functionality
4. **Monitor backend connectivity** for integration tests

## Next Steps

- Add more edge case testing
- Implement performance testing
- Add load testing for high-volume scenarios
- Enhance error scenario coverage

---
*Generated by run-sdk-tests.sh*
EOF

    print_success "Test report generated: $report_file"
}

# Show help
show_help() {
    echo "ADC Credit SDK Test Runner"
    echo ""
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  all         Run all SDK integration tests (default)"
    echo "  coverage    Run tests with coverage report"
    echo "  api-client  Run API client tests only"
    echo "  auth        Run authentication tests only"
    echo "  credits     Run credits tests only"
    echo "  shops       Run shops tests only"
    echo "  integration Run main integration tests only"
    echo "  install     Install dependencies only"
    echo "  check       Check backend connectivity only"
    echo "  report      Generate test report only"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                    # Run all tests"
    echo "  $0 coverage          # Run with coverage"
    echo "  $0 auth              # Run auth tests only"
    echo "  $0 check             # Check backend status"
}

# Main execution
main() {
    local command=${1:-all}
    
    case $command in
        "all")
            echo ""
            print_status "Starting SDK integration tests..."
            echo ""
            
            install_dependencies
            check_backend
            
            echo ""
            if run_all_sdk_tests; then
                generate_report
                print_success "SDK integration tests completed successfully! 🚀"
                exit 0
            else
                print_error "Some SDK tests failed. Please check the output above."
                exit 1
            fi
            ;;
        "coverage")
            install_dependencies
            check_backend
            run_with_coverage
            ;;
        "api-client")
            install_dependencies
            run_test_suite "src/__tests__/sdk/api-client.test.ts" "API Client Tests"
            ;;
        "auth")
            install_dependencies
            run_test_suite "src/__tests__/sdk/auth.test.ts" "Authentication Tests"
            ;;
        "credits")
            install_dependencies
            run_test_suite "src/__tests__/sdk/credits.test.ts" "Credits Tests"
            ;;
        "shops")
            install_dependencies
            run_test_suite "src/__tests__/sdk/shops.test.ts" "Shops Tests"
            ;;
        "integration")
            install_dependencies
            run_test_suite "src/__tests__/sdk/sdk-integration.test.ts" "Main SDK Integration Tests"
            ;;
        "install")
            install_dependencies
            ;;
        "check")
            check_backend
            ;;
        "report")
            generate_report
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "Unknown command: $command"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
