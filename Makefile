# Makefile for ADC Credit Application

# Default target
.PHONY: all
all: help

# Help message
.PHONY: help
help:
	@echo "ADC Credit Application Makefile"
	@echo ""
	@echo "Available commands:"
	@echo "  make dev                   - Start both frontend and backend servers in development mode"
	@echo "  make frontend              - Start only the frontend server"
	@echo "  make backend               - Start only the backend server"
	@echo "  make seed                  - Seed the database with test data"
	@echo "  make seed-merchant         - Seed merchant shops and credit codes for testing"
	@echo "  make migrate-org           - Migrate to organization structure"
	@echo "  make process-credit-resets - Process credit resets"
	@echo "  make seed-usage            - Generate test usage data (requires API key)"
	@echo "  make test                  - Run all tests"
	@echo "  make test-backend          - Run backend tests only"
	@echo "  make test-api              - Run API integration tests only"
	@echo "  make test-coverage         - Run tests with coverage report"
	@echo "  make test-subscription-guard - Run subscription guard integration tests"
	@echo "  make test-shop-limits      - Test shop creation limits"
	@echo "  make test-qr-code-limits   - Test QR code monthly limits"
	@echo "  make setup-test-db         - Setup test database"
	@echo "  make seed-subscription-tiers - Seed subscription tiers for testing"
	@echo "  make clean                 - Clean up temporary files"
	@echo "  make deploy-scheduled-credits - Deploy scheduled credits function to GCP"
	@echo "  make deploy-backend        - Deploy backend to GCP Cloud Run"
	@echo "  make deploy                - Deploy backend to GCP using unified cloudbuild.yaml"
	@echo "  make deploy-frontend       - Show instructions for deploying frontend to GCP Cloud Run"
	@echo "  make deploy-legacy         - Deploy everything to GCP using separate commands"
	@echo "  make docker-build          - Build Docker images for frontend and backend"
	@echo "  make docker-up             - Start Docker containers using docker-compose"
	@echo "  make docker-down           - Stop Docker containers"
	@echo "  make docker-logs           - Show Docker container logs"
	@echo "  make help                  - Show this help message"
	@echo ""
	@echo "Environment variables:"
	@echo "  API_KEY                    - API key for seeding usage data"
	@echo "  NUM_REQUESTS               - Number of requests to generate (default: 50)"
	@echo "  SCHEDULER_API_KEY          - API key for scheduled credits function"
	@echo "  DATABASE_URL               - PostgreSQL connection string"
	@echo "  PROJECT_ID                 - GCP project ID"
	@echo "  REGION                     - GCP region (default: us-central1)"
	@echo "  JWT_SECRET                 - Secret for JWT tokens"
	@echo "  GOOGLE_CLIENT_ID           - Google OAuth client ID"
	@echo "  GOOGLE_CLIENT_SECRET       - Google OAuth client secret"
	@echo "  FRONTEND_URL               - URL of the frontend application"
	@echo "  BACKEND_URL                - URL of the backend API"
	@echo "  NEXTAUTH_SECRET            - Secret for NextAuth authentication"

# Start both frontend and backend servers
.PHONY: dev
dev:
	@echo "Starting both frontend and backend servers..."
	@echo "Press Ctrl+C to stop all servers"
	@(trap 'kill 0' SIGINT; \
		echo "Starting backend server on port 8400..."; \
		cd backend && go run cmd/api/main.go & \
		BACKEND_PID=$$!; \
		echo "Backend server started with PID: $$BACKEND_PID"; \
		sleep 2; \
		echo "Starting frontend server on port 3800..."; \
		bun run dev & \
		FRONTEND_PID=$$!; \
		echo "Frontend server started with PID: $$FRONTEND_PID"; \
		wait)

# Start only the frontend server
.PHONY: frontend
frontend:
	@echo "Starting frontend server..."
	npm run dev

# Start only the backend server
.PHONY: backend
backend:
	@echo "Starting backend server..."
	cd backend && go run cmd/api/main.go

# Seed the database with test data
.PHONY: seed
seed:
	@echo "Seeding database with test data..."
	cd backend && go run cmd/seed/main.go

# Seed merchant shops and credit codes for testing
.PHONY: seed-merchant
seed-merchant:
	@echo "Seeding merchant shops and credit codes..."
	cd backend && go run cmd/seed_merchant/main.go

# Migrate to organization structure
.PHONY: migrate-org
migrate-org:
	@echo "Migrating to organization structure..."
	cd backend && go run cmd/tools/migrate_organization_structure.go

# Process credit resets
.PHONY: process-credit-resets
process-credit-resets:
	@echo "Processing credit resets..."
	cd backend && go run cmd/tools/process_credit_resets.go

# Generate test usage data
.PHONY: seed-usage
seed-usage:
	@if [ -z "$(API_KEY)" ]; then \
		echo "Error: API_KEY environment variable is required"; \
		echo "Usage: API_KEY=your_api_key [NUM_REQUESTS=50] make seed-usage"; \
		exit 1; \
	fi
	@echo "Generating test usage data with API key $(API_KEY)..."
	cd backend && API_KEY=$(API_KEY) NUM_REQUESTS=$(NUM_REQUESTS) ./scripts/seed_usage.sh

# Testing commands
# Run all tests
.PHONY: test
test: test-backend test-frontend
	@echo "All tests completed!"

# Run frontend tests only
.PHONY: test-frontend
test-frontend:
	@echo "Running frontend tests..."
	npm test

# Run SDK integration tests only
.PHONY: test-sdk
test-sdk:
	@echo "Running SDK integration tests..."
	npm run test:sdk

# Run frontend integration tests only
.PHONY: test-frontend-integration
test-frontend-integration:
	@echo "Running frontend integration tests..."
	npm run test:integration

# Run all frontend tests with coverage
.PHONY: test-frontend-coverage
test-frontend-coverage:
	@echo "Running frontend tests with coverage..."
	npm run test:coverage

# Run backend tests only
.PHONY: test-backend
test-backend:
	@echo "Running backend tests..."
	cd backend && go mod tidy
	cd backend && go test -v ./tests/...

# Run API integration tests only
.PHONY: test-api
test-api:
	@echo "Running API integration tests..."
	cd backend && go mod tidy
	cd backend && go test -v ./tests/api/...

# Run tests with coverage report
.PHONY: test-coverage
test-coverage:
	@echo "Running tests with coverage report..."
	cd backend && go mod tidy
	cd backend && go test -v -coverprofile=coverage.out ./tests/...
	cd backend && go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: backend/coverage.html"

# Clean up temporary files
.PHONY: clean
clean:
	@echo "Cleaning up temporary files..."
	rm -rf node_modules/.cache
	rm -rf .next
	find . -name "*.log" -type f -delete

# Deploy the scheduled credits function to GCP
.PHONY: deploy-scheduled-credits
deploy-scheduled-credits:
	@echo "Loading environment variables from .env file..."
	@if [ -f .env ]; then \
		export $$(grep -v '^#' .env | xargs); \
	fi; \
	if [ -z "$$SCHEDULER_API_KEY" ]; then \
		echo "Error: SCHEDULER_API_KEY environment variable is required"; \
		echo "Usage: SCHEDULER_API_KEY=your_api_key [DATABASE_URL=your_db_url] make deploy-scheduled-credits"; \
		exit 1; \
	fi; \
	echo "Deploying scheduled credits function to GCP..."; \
	cd backend && SCHEDULER_API_KEY=$$SCHEDULER_API_KEY DATABASE_URL=$$DATABASE_URL ./scripts/deploy_scheduled_credits.sh

# Deploy the backend to GCP Cloud Run
.PHONY: deploy-backend
deploy-backend:
	@echo "Loading environment variables from .env file..."
	@if [ -f .env ]; then \
		export $$(grep -v '^#' .env | xargs); \
	fi; \
	if [ -z "$$PROJECT_ID" ]; then \
		echo "Error: PROJECT_ID environment variable is required"; \
		echo "Usage: PROJECT_ID=your_gcp_project_id make deploy-backend"; \
		exit 1; \
	fi; \
	echo "Deploying backend to GCP Cloud Run..."; \
	gcloud builds submit --config=backend/cloudbuild.yaml \
		--substitutions=_DATABASE_URL="$$DATABASE_URL",_JWT_SECRET="$$JWT_SECRET",_GOOGLE_CLIENT_ID="$$GOOGLE_CLIENT_ID",_GOOGLE_CLIENT_SECRET="$$GOOGLE_CLIENT_SECRET",_FRONTEND_URL="$$FRONTEND_URL",_SCHEDULER_API_KEY="$$SCHEDULER_API_KEY"

# Deploy everything to GCP using separate commands
.PHONY: deploy-legacy
deploy-legacy: deploy-backend deploy-scheduled-credits
	@echo "Deployment completed successfully!"

# Deploy backend to GCP using unified cloudbuild.yaml
.PHONY: deploy
deploy:
	@echo "Loading environment variables from .env file..."
	@if [ -f .env ]; then \
		export $$(grep -v '^#' .env | xargs); \
	fi; \
	if [ -z "$$PROJECT_ID" ]; then \
		echo "Error: PROJECT_ID environment variable is required"; \
		echo "Usage: PROJECT_ID=your_gcp_project_id make deploy"; \
		exit 1; \
	fi; \
	echo "Deploying backend and scheduled credits to GCP..."; \
	REGION=$${REGION:-us-central1}; \
	gcloud builds submit --config=cloudbuild.yaml \
		--substitutions=_REGION="$$REGION",_DATABASE_URL="$$DATABASE_URL",_JWT_SECRET="$$JWT_SECRET",_GOOGLE_CLIENT_ID="$$GOOGLE_CLIENT_ID",_GOOGLE_CLIENT_SECRET="$$GOOGLE_CLIENT_SECRET",_FRONTEND_URL="$$FRONTEND_URL",_SCHEDULER_API_KEY="$$SCHEDULER_API_KEY"

# Deploy frontend to GCP Cloud Run
.PHONY: deploy-frontend
deploy-frontend:
	@echo "Loading environment variables from .env file..."
	@if [ -f .env ]; then \
		export $$(grep -v '^#' .env | xargs); \
	fi; \
	if [ -z "$$PROJECT_ID" ]; then \
		echo "Error: PROJECT_ID environment variable is required"; \
		echo "Usage: PROJECT_ID=your_gcp_project_id make deploy-frontend"; \
		exit 1; \
	fi; \
	echo "Building and deploying frontend to GCP Cloud Run..."; \
	echo "NOTE: This is a manual deployment process. For production, consider using a CI/CD pipeline."; \
	echo "1. Build the frontend locally"; \
	echo "2. Deploy to Cloud Run using the Google Cloud Console"; \
	echo "3. Set the following environment variables in the Cloud Run service:"; \
	echo "   - NEXT_PUBLIC_BACKEND_URL=$$BACKEND_URL"; \
	echo "   - NEXTAUTH_URL=$$FRONTEND_URL"; \
	echo "   - NEXTAUTH_SECRET=$$NEXTAUTH_SECRET"; \
	echo "   - GOOGLE_CLIENT_ID=$$GOOGLE_CLIENT_ID"; \
	echo "   - GOOGLE_CLIENT_SECRET=$$GOOGLE_CLIENT_SECRET"; \

	echo "   - NODE_ENV=production"

# Docker commands
# Build Docker images
.PHONY: docker-build
docker-build:
	@echo "Building Docker images..."
	docker build -t adc-credit-backend ./backend
	docker build -t adc-credit-frontend .
	@echo "Docker images built successfully!"

# Start Docker containers
.PHONY: docker-up
docker-up:
	@echo "Starting Docker containers..."
	docker-compose up -d
	@echo "Docker containers started successfully!"

# Stop Docker containers
.PHONY: docker-down
docker-down:
	@echo "Stopping Docker containers..."
	docker-compose down
	@echo "Docker containers stopped successfully!"

# Show Docker container logs
.PHONY: docker-logs
docker-logs:
	@echo "Showing Docker container logs..."
	docker-compose logs -f

# Subscription Guard Integration Tests
.PHONY: setup-test-db
setup-test-db:
	@echo "🗄️  Setting up test database..."
	@if [ ! -f backend/.env.test ]; then \
		echo "Creating test environment file..."; \
		cp backend/.env backend/.env.test; \
		sed -i 's/DB_NAME=adc_credit/DB_NAME=adc_credit_test/' backend/.env.test; \
	fi
	@echo "Test database setup complete"

.PHONY: seed-subscription-tiers
seed-subscription-tiers:
	@echo "🌱 Seeding subscription tiers..."
	cd backend && go run cmd/tools/migrate_subscription_tiers.go
	cd backend && go run cmd/tools/seed_subscription_tiers.go

.PHONY: test-subscription-guard
test-subscription-guard: setup-test-db seed-subscription-tiers
	@echo "🧪 Running subscription guard integration tests..."
	cd backend && go run tests/run_integration_tests.go

.PHONY: test-shop-limits
test-shop-limits: setup-test-db seed-subscription-tiers
	@echo "🏪 Testing shop creation limits..."
	cd backend && go test -v -run TestShopCreationLimits ./tests/integration/subscription_guard_test.go

.PHONY: test-customer-limits
test-customer-limits: setup-test-db seed-subscription-tiers
	@echo "👥 Testing customer limits..."
	cd backend && go test -v -run TestCustomerLimits ./tests/integration/subscription_guard_test.go

.PHONY: test-api-key-limits
test-api-key-limits: setup-test-db seed-subscription-tiers
	@echo "🔑 Testing API key limits..."
	cd backend && go test -v -run TestAPIKeyLimits ./tests/integration/subscription_guard_test.go

.PHONY: test-qr-code-limits
test-qr-code-limits: setup-test-db seed-subscription-tiers
	@echo "📱 Testing QR code limits..."
	cd backend && go test -v -run TestQRCodeGenerationLimits ./tests/integration/qr_code_limits_test.go

.PHONY: test-webhook-limits
test-webhook-limits: setup-test-db seed-subscription-tiers
	@echo "🪝 Testing webhook limits..."
	cd backend && go test -v -run TestWebhookLimits ./tests/integration/subscription_guard_test.go

.PHONY: test-subscription-api
test-subscription-api: setup-test-db seed-subscription-tiers
	@echo "🔌 Testing subscription limits API..."
	cd backend && go test -v ./tests/integration/subscription_limits_api_test.go

.PHONY: test-subscription-coverage
test-subscription-coverage: setup-test-db seed-subscription-tiers
	@echo "📊 Generating subscription guard test coverage..."
	cd backend && go test -coverprofile=subscription_coverage.out ./tests/integration/...
	cd backend && go tool cover -html=subscription_coverage.out -o subscription_coverage.html
	cd backend && go tool cover -func=subscription_coverage.out
	@echo "📈 Coverage report generated: backend/subscription_coverage.html"
