
'use client';

import { motion } from 'framer-motion';
import {
  IntegrationSection,
  PricingSection,
  TestimonialsSection,
  CallToActionSection,
  LandingFooter
} from '@/components/landing';
import { HeroSection } from '@/components/ui/lamp';
import { FeaturesSectionWithHoverEffects } from '@/components/blocks/feature-section-with-hover-effects';

// Main App Component
export default function App() {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="bg-background"
    >
      <div
        className="relative flex size-full min-h-screen flex-col group/design-root overflow-x-hidden"
        style={{ fontFamily: 'Inter, "Noto Sans", sans-serif' }}
      >
        <main className="flex flex-1 justify-center py-10 md:py-16 lg:py-20">
          <div className="layout-content-container flex flex-col max-w-6xl flex-1 gap-16 md:gap-24 lg:gap-32 px-6 md:px-10">
            <HeroSection />
            <FeaturesSectionWithHoverEffects />
            <IntegrationSection />
            <PricingSection />
            <TestimonialsSection />
            <CallToActionSection />
          </div>
        </main>
        <LandingFooter />
      </div>
    </motion.div>
  );
}