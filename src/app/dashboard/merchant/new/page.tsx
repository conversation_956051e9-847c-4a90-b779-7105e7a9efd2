"use client";

import { useRouter } from "next/navigation";
import { useToast } from "@/components/ui/use-toast";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Storefront, EnvelopeSimple, Phone, TextT } from "phosphor-react";
import { useCreateShopMutation } from "@/lib/api/apiSlice";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { MerchantBreadcrumbs } from "@/components/navigation/breadcrumbs";
import { MerchantPageHeader } from "@/components/layout/page-header-mobile";

// Define form schema with Zod
const formSchema = z.object({
  name: z.string().min(1, { message: "Shop name is required" }),
  description: z.string().optional(),
  contact_email: z.string().email({ message: "Invalid email address" }).optional().or(z.literal("")),
  contact_phone: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

export default function NewShopPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [createShop, { isLoading }] = useCreateShopMutation();

  // Initialize React Hook Form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      contact_email: "",
      contact_phone: "",
    },
  });

  const onSubmit = async (data: FormValues) => {
    try {
      const result = await createShop({
        name: data.name,
        description: data.description,
        contact_email: data.contact_email,
        contact_phone: data.contact_phone
      }).unwrap();

      toast({
        title: "Success",
        description: "Shop created successfully",
      });

      // Navigate to the new shop's detail page using slug
      router.push(`/dashboard/merchant/shop/${result.slug}`);
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.data?.message || "Failed to create shop",
        variant: "destructive",
      });
    }
  };

  // Breadcrumb segments
  const breadcrumbSegments = [
    { label: "Dashboard", href: "/dashboard/merchant" },
    { label: "New Shop", href: "/dashboard/merchant/new" },
  ];

  return (
    <div className="space-y-6">
      {/* Breadcrumbs - Desktop only */}
      <div className="hidden md:block">
        <MerchantBreadcrumbs segments={breadcrumbSegments} />
      </div>

      {/* Mobile Header */}
      <div className="md:hidden">
        <MerchantPageHeader
          title="New Shop"
          backHref="/dashboard/merchant"
        />
      </div>

      {/* Page Header - Desktop only */}
      <div className="hidden md:block">
        <h1 className="text-2xl font-bold tracking-tight md:text-3xl text-[#181510]">
          Create New Shop
        </h1>
        <p className="text-[#8a745c] mt-1">
          Add a new shop to your merchant account
        </p>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Storefront className="mr-2 h-5 w-5" />
            Shop Information
          </CardTitle>
          <CardDescription>
            Enter the details for your new shop
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form id="new-shop-form" onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Grid layout: 2 columns on desktop, 1 column on tablet/mobile */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Left Column */}
                <div className="space-y-6">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center">
                          <TextT className="mr-2 h-4 w-4" />
                          Shop Name
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter shop name"
                            className="bg-[#f1edea]"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription className="text-xs text-[#8a745c]">
                          This is how your shop will appear to customers
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="contact_email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center">
                          <EnvelopeSimple className="mr-2 h-4 w-4" />
                          Contact Email
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="email"
                            placeholder="Enter contact email"
                            className="bg-[#f1edea]"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription className="text-xs text-[#8a745c]">
                          Email address for customer inquiries
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Right Column */}
                <div className="space-y-6">
                  <FormField
                    control={form.control}
                    name="contact_phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center">
                          <Phone className="mr-2 h-4 w-4" />
                          Contact Phone
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="tel"
                            placeholder="Enter contact phone"
                            className="bg-[#f1edea]"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription className="text-xs text-[#8a745c]">
                          Phone number for customer inquiries
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Full width description field */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center">
                      <TextT className="mr-2 h-4 w-4" />
                      Description
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter shop description"
                        className="bg-[#f1edea] min-h-24"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription className="text-xs text-[#8a745c]">
                      Describe your shop and what you offer
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </form>
          </Form>
        </CardContent>
        <CardFooter className="flex flex-col sm:flex-row gap-3 items-start sm:items-center">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push('/dashboard/merchant')}
            className="w-full sm:w-auto"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            form="new-shop-form"
            className="w-full sm:w-auto bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
            disabled={isLoading}
          >
            {isLoading ? "Creating..." : "Create Shop"}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
