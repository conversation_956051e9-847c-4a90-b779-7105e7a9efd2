/**
 * Authentication Module Integration Tests
 * Tests the SDK authentication functionality
 */

import { 
  createTestSDKNoAuth,
  createTestSDKWithToken,
  TEST_USER,
  generateTestEmail,
  isBackendRunning,
  skipIfBackendNotRunning,
  assertions,
  TestDataCleanup,
  mockFetchAPIError,
  createMockResponse
} from './test-utils';

describe('Authentication Module Integration Tests', () => {
  let cleanup: TestDataCleanup;

  beforeEach(() => {
    cleanup = new TestDataCleanup();
  });

  afterEach(async () => {
    await cleanup.cleanup();
  });

  describe('SDK Initialization for Auth', () => {
    it('should create SDK instance without authentication', () => {
      const sdk = createTestSDKNoAuth();
      expect(sdk.auth).toBeDefined();
    });

    it('should create SDK instance with token', () => {
      const sdk = createTestSDKWithToken();
      expect(sdk.auth).toBeDefined();
    });
  });

  describe('Login with Email and Password', () => {
    beforeEach(async () => {
      await skipIfBackendNotRunning();
    });

    it('should login with valid credentials (mocked)', async () => {
      const sdk = createTestSDKNoAuth();
      
      // Mock successful login response
      const mockResponse = {
        token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.token',
        user: {
          id: 'user-123',
          email: TEST_USER.email,
          name: TEST_USER.name,
          picture: 'https://example.com/avatar.jpg',
          role: 'user',
        },
      };

      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(mockResponse)
      );
      global.fetch = mockFetch;

      const response = await sdk.auth.login(TEST_USER.email, TEST_USER.password);

      assertions.assertSuccess(response);
      expect(response.data.token).toBeDefined();
      expect(response.data.user.email).toBe(TEST_USER.email);
      assertions.assertValidJWTToken(response.data.token);
    });

    it('should handle invalid credentials', async () => {
      const sdk = createTestSDKNoAuth();
      
      const restoreFetch = mockFetchAPIError(401, 'Invalid credentials');

      const response = await sdk.auth.login('<EMAIL>', 'wrongpassword');

      assertions.assertError(response);
      expect(response.error).toContain('Invalid credentials');

      restoreFetch();
    });

    it('should handle missing email', async () => {
      const sdk = createTestSDKNoAuth();
      
      const restoreFetch = mockFetchAPIError(400, 'Email is required');

      const response = await sdk.auth.login('', TEST_USER.password);

      assertions.assertError(response);
      expect(response.error).toContain('Email is required');

      restoreFetch();
    });

    it('should handle missing password', async () => {
      const sdk = createTestSDKNoAuth();
      
      const restoreFetch = mockFetchAPIError(400, 'Password is required');

      const response = await sdk.auth.login(TEST_USER.email, '');

      assertions.assertError(response);
      expect(response.error).toContain('Password is required');

      restoreFetch();
    });
  });

  describe('Google Authentication', () => {
    beforeEach(async () => {
      await skipIfBackendNotRunning();
    });

    it('should login with Google token (mocked)', async () => {
      const sdk = createTestSDKNoAuth();
      
      const mockResponse = {
        token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.google.token',
        user: {
          id: 'user-456',
          email: '<EMAIL>',
          name: 'Google User',
          picture: 'https://lh3.googleusercontent.com/avatar.jpg',
          role: 'user',
        },
      };

      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(mockResponse)
      );
      global.fetch = mockFetch;

      const googleToken = 'google-oauth-token-123';
      const response = await sdk.auth.loginWithGoogle(googleToken);

      assertions.assertSuccess(response);
      expect(response.data.token).toBeDefined();
      expect(response.data.user.email).toBe('<EMAIL>');
      assertions.assertValidJWTToken(response.data.token);
    });

    it('should handle invalid Google token', async () => {
      const sdk = createTestSDKNoAuth();
      
      const restoreFetch = mockFetchAPIError(401, 'Invalid Google token');

      const response = await sdk.auth.loginWithGoogle('invalid-google-token');

      assertions.assertError(response);
      expect(response.error).toContain('Invalid Google token');

      restoreFetch();
    });

    it('should handle missing Google token', async () => {
      const sdk = createTestSDKNoAuth();
      
      const restoreFetch = mockFetchAPIError(400, 'Google token is required');

      const response = await sdk.auth.loginWithGoogle('');

      assertions.assertError(response);
      expect(response.error).toContain('Google token is required');

      restoreFetch();
    });
  });

  describe('Token Refresh', () => {
    beforeEach(async () => {
      await skipIfBackendNotRunning();
    });

    it('should refresh token with valid refresh token (mocked)', async () => {
      const sdk = createTestSDKNoAuth();
      
      const mockResponse = {
        token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.refreshed.token',
      };

      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(mockResponse)
      );
      global.fetch = mockFetch;

      const refreshToken = 'valid-refresh-token-123';
      const response = await sdk.auth.refreshToken(refreshToken);

      assertions.assertSuccess(response);
      expect(response.data.token).toBeDefined();
      assertions.assertValidJWTToken(response.data.token);
    });

    it('should handle invalid refresh token', async () => {
      const sdk = createTestSDKNoAuth();
      
      const restoreFetch = mockFetchAPIError(401, 'Invalid refresh token');

      const response = await sdk.auth.refreshToken('invalid-refresh-token');

      assertions.assertError(response);
      expect(response.error).toContain('Invalid refresh token');

      restoreFetch();
    });

    it('should handle expired refresh token', async () => {
      const sdk = createTestSDKNoAuth();
      
      const restoreFetch = mockFetchAPIError(401, 'Refresh token expired');

      const response = await sdk.auth.refreshToken('expired-refresh-token');

      assertions.assertError(response);
      expect(response.error).toContain('Refresh token expired');

      restoreFetch();
    });
  });

  describe('User Registration', () => {
    beforeEach(async () => {
      await skipIfBackendNotRunning();
    });

    it('should register new user (mocked)', async () => {
      const sdk = createTestSDKNoAuth();
      
      const newUser = {
        email: generateTestEmail(),
        password: 'newpassword123',
        name: 'New User',
      };

      const mockResponse = {
        token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.new.token',
        user: {
          id: 'user-789',
          email: newUser.email,
          name: newUser.name,
          picture: '',
          role: 'user',
        },
      };

      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(mockResponse)
      );
      global.fetch = mockFetch;

      const response = await sdk.auth.register(newUser.email, newUser.password, newUser.name);

      assertions.assertSuccess(response);
      expect(response.data.token).toBeDefined();
      expect(response.data.user.email).toBe(newUser.email);
      expect(response.data.user.name).toBe(newUser.name);
    });

    it('should handle duplicate email registration', async () => {
      const sdk = createTestSDKNoAuth();
      
      const restoreFetch = mockFetchAPIError(409, 'Email already exists');

      const response = await sdk.auth.register(
        '<EMAIL>',
        'password123',
        'Existing User'
      );

      assertions.assertError(response);
      expect(response.error).toContain('Email already exists');

      restoreFetch();
    });

    it('should handle weak password', async () => {
      const sdk = createTestSDKNoAuth();
      
      const restoreFetch = mockFetchAPIError(400, 'Password too weak');

      const response = await sdk.auth.register(
        generateTestEmail(),
        '123',
        'Test User'
      );

      assertions.assertError(response);
      expect(response.error).toContain('Password too weak');

      restoreFetch();
    });
  });

  describe('Password Reset', () => {
    beforeEach(async () => {
      await skipIfBackendNotRunning();
    });

    it('should request password reset (mocked)', async () => {
      const sdk = createTestSDKNoAuth();
      
      const mockResponse = {
        message: 'Password reset email sent',
      };

      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(mockResponse)
      );
      global.fetch = mockFetch;

      const response = await sdk.auth.forgotPassword(TEST_USER.email);

      assertions.assertSuccess(response);
      expect(response.data.message).toContain('Password reset email sent');
    });

    it('should handle non-existent email for password reset', async () => {
      const sdk = createTestSDKNoAuth();
      
      const restoreFetch = mockFetchAPIError(404, 'Email not found');

      const response = await sdk.auth.forgotPassword('<EMAIL>');

      assertions.assertError(response);
      expect(response.error).toContain('Email not found');

      restoreFetch();
    });

    it('should reset password with valid token (mocked)', async () => {
      const sdk = createTestSDKNoAuth();
      
      const mockResponse = {
        message: 'Password reset successful',
      };

      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(mockResponse)
      );
      global.fetch = mockFetch;

      const response = await sdk.auth.resetPassword('valid-reset-token', 'newpassword123');

      assertions.assertSuccess(response);
      expect(response.data.message).toContain('Password reset successful');
    });

    it('should handle invalid reset token', async () => {
      const sdk = createTestSDKNoAuth();
      
      const restoreFetch = mockFetchAPIError(400, 'Invalid or expired reset token');

      const response = await sdk.auth.resetPassword('invalid-token', 'newpassword123');

      assertions.assertError(response);
      expect(response.error).toContain('Invalid or expired reset token');

      restoreFetch();
    });
  });
});
