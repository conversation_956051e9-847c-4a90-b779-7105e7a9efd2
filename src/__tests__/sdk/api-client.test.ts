/**
 * API Client Integration Tests
 * Tests the core API client functionality
 */

import { ApiClient } from '@/sdk/client';
import { 
  TEST_CONFIG, 
  createMockResponse, 
  mockFetchNetworkError, 
  mockFetchAPIError,
  isBackendRunning,
  skipIfBackendNotRunning 
} from './test-utils';

describe('API Client Integration Tests', () => {
  let client: ApiClient;

  beforeEach(() => {
    client = new ApiClient(TEST_CONFIG);
  });

  describe('Initialization', () => {
    it('should initialize with default configuration', () => {
      const defaultClient = new ApiClient();
      expect(defaultClient).toBeDefined();
    });

    it('should initialize with custom configuration', () => {
      const customClient = new ApiClient({
        apiUrl: 'https://custom-api.example.com',
        apiKey: 'custom-key',
        debug: true,
      });
      expect(customClient).toBeDefined();
    });

    it('should format API URL correctly', () => {
      const clientWithTrailingSlash = new ApiClient({
        apiUrl: 'http://localhost:8400/',
      });
      expect(clientWithTrailingSlash).toBeDefined();
    });
  });

  describe('Authentication Headers', () => {
    it('should add API key header when configured', async () => {
      const clientWithApiKey = new ApiClient({
        apiUrl: TEST_CONFIG.apiUrl,
        apiKey: 'test-api-key',
      });

      // Mock fetch to capture headers
      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse({ success: true })
      );
      global.fetch = mockFetch;

      await clientWithApiKey.get('/test');

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: expect.objectContaining({
            'X-API-Key': 'test-api-key',
          }),
        })
      );
    });

    it('should add Authorization header when token is configured', async () => {
      const clientWithToken = new ApiClient({
        apiUrl: TEST_CONFIG.apiUrl,
        token: 'test-jwt-token',
      });

      // Mock fetch to capture headers
      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse({ success: true })
      );
      global.fetch = mockFetch;

      await clientWithToken.get('/test');

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': 'Bearer test-jwt-token',
          }),
        })
      );
    });
  });

  describe('HTTP Methods', () => {
    beforeEach(async () => {
      await skipIfBackendNotRunning();
    });

    it('should make GET requests', async () => {
      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse({ message: 'GET success' })
      );
      global.fetch = mockFetch;

      const response = await client.get('/test');

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/test'),
        expect.objectContaining({
          method: 'GET',
        })
      );
      expect(response.data).toEqual({ message: 'GET success' });
    });

    it('should make POST requests with data', async () => {
      const testData = { name: 'test', value: 123 };
      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse({ message: 'POST success' })
      );
      global.fetch = mockFetch;

      const response = await client.post('/test', testData);

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/test'),
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(testData),
        })
      );
      expect(response.data).toEqual({ message: 'POST success' });
    });

    it('should make PUT requests with data', async () => {
      const testData = { id: 1, name: 'updated' };
      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse({ message: 'PUT success' })
      );
      global.fetch = mockFetch;

      const response = await client.put('/test/1', testData);

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/test/1'),
        expect.objectContaining({
          method: 'PUT',
          body: JSON.stringify(testData),
        })
      );
      expect(response.data).toEqual({ message: 'PUT success' });
    });

    it('should make DELETE requests', async () => {
      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse({ message: 'DELETE success' })
      );
      global.fetch = mockFetch;

      const response = await client.delete('/test/1');

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/test/1'),
        expect.objectContaining({
          method: 'DELETE',
        })
      );
      expect(response.data).toEqual({ message: 'DELETE success' });
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors', async () => {
      const restoreFetch = mockFetchNetworkError();

      const response = await client.get('/test');

      expect(response.error).toBeDefined();
      expect(response.error).toContain('Network error');
      expect(response.data).toBeUndefined();

      restoreFetch();
    });

    it('should handle HTTP error responses', async () => {
      const restoreFetch = mockFetchAPIError(404, 'Not found');

      const response = await client.get('/test');

      expect(response.error).toBeDefined();
      expect(response.error).toContain('Not found');
      expect(response.data).toBeUndefined();

      restoreFetch();
    });

    it('should handle 401 unauthorized errors', async () => {
      const restoreFetch = mockFetchAPIError(401, 'Unauthorized');

      const response = await client.get('/test');

      expect(response.error).toBeDefined();
      expect(response.error).toContain('Unauthorized');

      restoreFetch();
    });

    it('should handle 500 server errors', async () => {
      const restoreFetch = mockFetchAPIError(500, 'Internal server error');

      const response = await client.get('/test');

      expect(response.error).toBeDefined();
      expect(response.error).toContain('Internal server error');

      restoreFetch();
    });
  });

  describe('Request Configuration', () => {
    it('should include custom headers', async () => {
      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse({ success: true })
      );
      global.fetch = mockFetch;

      await client.get('/test', {
        headers: {
          'Custom-Header': 'custom-value',
        },
      });

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Custom-Header': 'custom-value',
            'Content-Type': 'application/json',
          }),
        })
      );
    });

    it('should handle query parameters in URL', async () => {
      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse({ success: true })
      );
      global.fetch = mockFetch;

      await client.get('/test?param1=value1&param2=value2');

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('param1=value1&param2=value2'),
        expect.any(Object)
      );
    });
  });

  describe('Real Backend Integration', () => {
    beforeEach(async () => {
      const isRunning = await isBackendRunning();
      if (!isRunning) {
        console.warn('Backend server is not running. Skipping real integration tests.');
        return;
      }
    });

    it('should connect to health endpoint', async () => {
      const isRunning = await isBackendRunning();
      if (!isRunning) {
        console.warn('Skipping test - backend not running');
        return;
      }

      const healthClient = new ApiClient({
        apiUrl: TEST_CONFIG.apiUrl,
      });

      const response = await healthClient.get('/api/v1/health');
      
      // Health endpoint should be accessible without authentication
      expect(response.error).toBeUndefined();
      expect(response.data).toBeDefined();
    });
  });
});
