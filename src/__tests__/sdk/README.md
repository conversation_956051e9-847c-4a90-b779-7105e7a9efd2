# ADC Credit SDK Integration Tests

This directory contains comprehensive integration tests for the ADC Credit SDK, ensuring all SDK functionality works correctly with the backend API.

## 🎯 Overview

The SDK integration tests verify:
- **Core SDK functionality** - Authentication, API client, error handling
- **All SDK modules** - Credits, Shops, Auth, API Keys, Users, etc.
- **Real API integration** - Connection to backend API endpoints
- **Error scenarios** - Network failures, authentication errors, validation errors
- **End-to-end workflows** - Complete user journeys using the SDK

## 📁 Test Structure

```
src/__tests__/sdk/
├── README.md                    # This file
├── test-utils.ts               # Test utilities and helpers
├── api-client.test.ts          # API client core functionality tests
├── auth.test.ts                # Authentication module tests
├── credits.test.ts             # Credits module tests
├── shops.test.ts               # Shops module tests
└── sdk-integration.test.ts     # Main SDK integration tests
```

## 🧪 Test Categories

### 1. API Client Tests (`api-client.test.ts`)
Tests the core API client functionality including:
- ✅ SDK initialization with different configurations
- ✅ HTTP methods (GET, POST, PUT, DELETE)
- ✅ Authentication headers (API key and JWT token)
- ✅ Error handling (network errors, HTTP errors)
- ✅ Request configuration and custom headers

### 2. Authentication Tests (`auth.test.ts`)
Tests the authentication module including:
- ✅ Email/password login
- ✅ Google OAuth authentication
- ✅ Token refresh functionality
- ✅ User registration
- ✅ Password reset workflow
- ✅ Error handling for invalid credentials

### 3. Credits Tests (`credits.test.ts`)
Tests the credits management functionality including:
- ✅ Get credit balance and subscription info
- ✅ Transaction history retrieval
- ✅ Credit top-ups and payments
- ✅ Credit consumption (external API)
- ✅ Scheduled credits management
- ✅ Error handling for insufficient credits

### 4. Shops Tests (`shops.test.ts`)
Tests the unified shops functionality including:
- ✅ Shop CRUD operations (Create, Read, Update, Delete)
- ✅ Shop customer management
- ✅ Shop credit management and transactions
- ✅ Credit code generation and management
- ✅ Shop API key management
- ✅ Shop statistics and analytics
- ✅ Error handling and subscription limits

### 5. Main SDK Integration Tests (`sdk-integration.test.ts`)
Tests complete end-to-end workflows including:
- ✅ User registration → Shop creation → Credit management
- ✅ Shop customer → Credit addition → Code generation
- ✅ API key creation → Update → Deletion
- ✅ Cross-module error handling
- ✅ Real backend connectivity

## 🚀 Running Tests

### Prerequisites

1. **Node.js and npm/bun** - Ensure Node.js is installed
2. **Backend API** - Backend server should be running on `http://localhost:8400`
3. **Test Dependencies** - Install test dependencies

### Quick Start

```bash
# Install dependencies
npm install

# Run all SDK tests
npm run test:sdk

# Run specific test file
npm test src/__tests__/sdk/auth.test.ts

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

### Using Make Commands

```bash
# Run SDK integration tests
make test-sdk

# Run all frontend tests
make test-frontend

# Run frontend tests with coverage
make test-frontend-coverage
```

### Manual Test Execution

```bash
# Run individual test suites
npx jest src/__tests__/sdk/api-client.test.ts
npx jest src/__tests__/sdk/auth.test.ts
npx jest src/__tests__/sdk/credits.test.ts
npx jest src/__tests__/sdk/shops.test.ts
npx jest src/__tests__/sdk/sdk-integration.test.ts

# Run with verbose output
npx jest src/__tests__/sdk --verbose

# Run specific test case
npx jest src/__tests__/sdk/auth.test.ts -t "should login with valid credentials"
```

## 🔧 Configuration

### Environment Variables

The tests use the following environment variables:

```bash
# Test environment (automatically set)
NODE_ENV=test

# Backend API URL
TEST_API_URL=http://localhost:8400

# Test authentication credentials
TEST_API_KEY=test-api-key-for-integration-tests
TEST_JWT_TOKEN=test-jwt-token-for-integration-tests
```

### Jest Configuration

Tests are configured in `jest.config.js` with:
- **Test Environment**: Node.js environment for API testing
- **Test Timeout**: 30 seconds for integration tests
- **Coverage**: SDK modules coverage reporting
- **Mocking**: Global fetch mocking for network requests

## 📊 Test Data

### Test Users
- **Email**: `<EMAIL>`
- **Password**: `testpassword123`
- **Name**: `Test User`

### Test Shops
- **Name**: `Test Shop` (with unique identifiers)
- **Type**: `retail`, `api_service`, or `enterprise`
- **Contact**: `<EMAIL>`, `+1234567890`

### Test Customers
- **Name**: `Test Customer`
- **Email**: Unique generated emails
- **Phone**: `+1234567890`

## 🎭 Mocking Strategy

The tests use a hybrid approach:

### 1. **Mocked Tests** (Default)
- Mock HTTP responses for predictable testing
- Test SDK logic and error handling
- Fast execution without backend dependency
- Comprehensive coverage of edge cases

### 2. **Real Backend Tests** (Optional)
- Connect to actual backend API when available
- Test real integration scenarios
- Verify end-to-end connectivity
- Automatically skipped if backend is not running

### 3. **Error Simulation**
- Mock network failures
- Mock API error responses
- Test authentication failures
- Test rate limiting scenarios

## 🛠️ Test Utilities

The `test-utils.ts` file provides:

### SDK Factory Functions
- `createTestSDKWithAPIKey()` - SDK with API key auth
- `createTestSDKWithToken()` - SDK with JWT token auth
- `createTestSDKNoAuth()` - SDK without authentication

### Mock Helpers
- `createMockResponse()` - Create mock HTTP responses
- `mockFetchNetworkError()` - Simulate network errors
- `mockFetchAPIError()` - Simulate API errors

### Data Generators
- `generateTestId()` - Unique test identifiers
- `generateTestEmail()` - Unique test emails
- `generateTestShopName()` - Unique shop names

### Assertion Helpers
- `assertions.assertSuccess()` - Assert successful responses
- `assertions.assertError()` - Assert error responses
- `assertions.assertValidUUID()` - Assert valid UUID format
- `assertions.assertValidAPIKey()` - Assert valid API key format

### Cleanup Management
- `TestDataCleanup` - Manage test data cleanup
- Automatic cleanup after each test

## 🔍 Debugging Tests

### Enable Debug Mode
```bash
# Run tests with debug output
DEBUG=1 npm run test:sdk

# Run specific test with verbose output
npx jest src/__tests__/sdk/auth.test.ts --verbose
```

### Check Backend Connectivity
```bash
# Test if backend is running
curl http://localhost:8400/api/v1/health

# Start backend server
cd backend && go run cmd/api/main.go
```

### Common Issues

1. **Backend Not Running**: Tests will be skipped automatically
2. **Network Errors**: Check backend server status
3. **Authentication Errors**: Verify test API keys and tokens
4. **Timeout Errors**: Increase test timeout in Jest config

## 📈 Coverage Reports

Generate coverage reports:

```bash
# Generate HTML coverage report
npm run test:coverage

# View coverage report
open coverage/lcov-report/index.html
```

Coverage targets:
- **SDK Modules**: 90%+ coverage
- **Core Functionality**: 95%+ coverage
- **Error Handling**: 85%+ coverage

## 🚀 Continuous Integration

The tests are designed to work in CI/CD environments:

### GitHub Actions
```yaml
- name: Run SDK Tests
  run: npm run test:sdk

- name: Generate Coverage
  run: npm run test:coverage
```

### Docker Testing
```bash
# Run tests in Docker container
docker run --rm -v $(pwd):/app -w /app node:18 npm run test:sdk
```

## 🤝 Contributing

When adding new SDK functionality:

1. **Add corresponding tests** in the appropriate test file
2. **Update test utilities** if new helpers are needed
3. **Mock external dependencies** for predictable testing
4. **Test both success and error scenarios**
5. **Update this README** with new test descriptions

### Test Naming Convention
- Use descriptive test names: `should create shop with valid data`
- Group related tests in `describe` blocks
- Use `it` for individual test cases
- Include expected behavior in test names
