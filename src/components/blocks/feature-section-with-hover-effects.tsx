import { cn } from "@/lib/utils";
import {
  IconQrcode,
  IconShoppingBag,
  IconUsers,
  IconChartBar,
  IconCreditCard,
  IconShield,
  IconDeviceMobile,
  IconApi,
} from "@tabler/icons-react";

export function FeaturesSectionWithHoverEffects() {
  const features = [
    {
      title: "QR Code Payments",
      description:
        "Generate secure QR codes for instant credit transactions and seamless customer payments.",
      icon: <IconQrcode />,
    },
    {
      title: "Shop Management",
      description:
        "Create and manage multiple shops with branches, inventory, and customer relationships.",
      icon: <IconShoppingBag />,
    },
    {
      title: "Customer Analytics",
      description:
        "Track customer behavior, spending patterns, and engagement metrics in real-time.",
      icon: <IconUsers />,
    },
    {
      title: "Business Insights",
      description: "Comprehensive analytics and reporting to drive business growth and decisions.",
      icon: <IconChartBar />,
    },
    {
      title: "Credit Management",
      description: "Flexible credit system with top-ups, transfers, and transaction tracking.",
      icon: <IconCreditCard />,
    },
    {
      title: "Secure & Reliable",
      description:
        "Enterprise-grade security with API key management and encrypted transactions.",
      icon: <IconShield />,
    },
    {
      title: "Mobile Optimized",
      description:
        "Responsive design that works perfectly on all devices and screen sizes.",
      icon: <IconDeviceMobile />,
    },
    {
      title: "Developer SDK",
      description: "Comprehensive SDK with TypeScript support for easy integration.",
      icon: <IconApi />,
    },
  ];
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4  relative z-10 py-10 max-w-7xl mx-auto">
      {features.map((feature, index) => (
        <Feature key={feature.title} {...feature} index={index} />
      ))}
    </div>
  );
}

const Feature = ({
  title,
  description,
  icon,
  index,
}: {
  title: string;
  description: string;
  icon: React.ReactNode;
  index: number;
}) => {
  return (
    <div
      className={cn(
        "flex flex-col lg:border-r py-10 relative group/feature border-border",
        (index === 0 || index === 4) && "lg:border-l border-border",
        index < 4 && "lg:border-b border-border"
      )}
    >
      {index < 4 && (
        <div className="opacity-0 group-hover/feature:opacity-100 transition duration-200 absolute inset-0 h-full w-full bg-gradient-to-t from-accent/20 to-transparent pointer-events-none" />
      )}
      {index >= 4 && (
        <div className="opacity-0 group-hover/feature:opacity-100 transition duration-200 absolute inset-0 h-full w-full bg-gradient-to-b from-accent/20 to-transparent pointer-events-none" />
      )}
      <div className="mb-4 relative z-10 px-10 text-muted-foreground">
        {icon}
      </div>
      <div className="text-lg font-bold mb-2 relative z-10 px-10">
        <div className="absolute left-0 inset-y-0 h-6 group-hover/feature:h-8 w-1 rounded-tr-full rounded-br-full bg-muted group-hover/feature:bg-primary transition-all duration-200 origin-center" />
        <span className="group-hover/feature:translate-x-2 transition duration-200 inline-block text-foreground">
          {title}
        </span>
      </div>
      <p className="text-sm text-muted-foreground max-w-xs relative z-10 px-10">
        {description}
      </p>
    </div>
  );
};
