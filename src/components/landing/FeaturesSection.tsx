export const FeaturesSection = () => {
  const features = [
    {
      icon: (
        <svg fill="currentColor" height="32px" viewBox="0 0 256 256" width="32px" xmlns="http://www.w3.org/2000/svg">
          <path d="M208,32H48A16,16,0,0,0,32,48V208a16,16,0,0,0,16,16H208a16,16,0,0,0,16-16V48A16,16,0,0,0,208,32ZM48,48H208V88H48ZM48,208V104H208V208Z"></path>
        </svg>
      ),
      title: "Shop Management",
      description: "Create and manage multiple shops with branches, API keys, and customer bases."
    },
    {
      icon: (
        <svg fill="currentColor" height="32px" viewBox="0 0 256 256" width="32px" xmlns="http://www.w3.org/2000/svg">
          <path d="M208,40H48A24,24,0,0,0,24,64V192a24,24,0,0,0,24,24H208a24,24,0,0,0,24-24V64A24,24,0,0,0,208,40ZM40,64a8,8,0,0,1,8-8H208a8,8,0,0,1,8,8v8H40ZM208,200H48a8,8,0,0,1-8-8V88H216V192A8,8,0,0,1,208,200ZM64,112a8,8,0,0,1,8-8H184a8,8,0,0,1,0,16H72A8,8,0,0,1,64,112Zm0,32a8,8,0,0,1,8-8h80a8,8,0,0,1,0,16H72A8,8,0,0,1,64,144Z"></path>
        </svg>
      ),
      title: "QR Code Payments",
      description: "Generate secure QR codes for instant credit transactions and payments."
    },
    {
      icon: (
        <svg fill="currentColor" height="32px" viewBox="0 0 256 256" width="32px" xmlns="http://www.w3.org/2000/svg">
          <path d="M117.25,157.92a60,60,0,1,0-66.5,0A95.83,95.83,0,0,0,3.53,195.63a8,8,0,1,0,13.4,8.74,80,80,0,0,1,134.14,0,8,8,0,0,0,13.4-8.74A95.83,95.83,0,0,0,117.25,157.92ZM40,108a44,44,0,1,1,44,44A44.05,44.05,0,0,1,40,108Zm210.14,98.7a8,8,0,0,1-11.07-2.33A79.83,79.83,0,0,0,172,168a8,8,0,0,1,0-16,44,44,0,1,0-16.34-84.87,8,8,0,1,1-5.94-14.85,60,60,0,0,1,55.53,105.64,95.83,95.83,0,0,1,47.22,37.71A8,8,0,0,1,250.14,206.7Z"></path>
        </svg>
      ),
      title: "Customer Management",
      description: "Track customer interactions, credit balances, and transaction history."
    },
    {
      icon: (
        <svg fill="currentColor" height="32px" viewBox="0 0 256 256" width="32px" xmlns="http://www.w3.org/2000/svg">
          <path d="M232,208a8,8,0,0,1-8,8H32a8,8,0,0,1-8-8V48a8,8,0,0,1,16,0v94.37L90.73,98a8,8,0,0,1,10.07-.38l58.81,44.11L218.73,90a8,8,0,1,1,10.54,12l-64,56a8,8,0,0,1-10.07.38L96.39,114.29,40,163.63V200H224A8,8,0,0,1,232,208Z"></path>
        </svg>
      ),
      title: "Analytics & Insights",
      description: "Comprehensive analytics on transactions, usage patterns, and business growth."
    }
  ];

  return (
    <section className="flex flex-col gap-8 md:gap-12 items-center">
      <div className="text-center max-w-3xl">
        <h2 className="text-foreground text-3xl md:text-4xl font-bold leading-tight tracking-tight mb-3">Complete Shop Credit Solution</h2>
        <p className="text-muted-foreground text-base md:text-lg leading-relaxed">
          Everything you need to run a modern credit-based business. From QR code payments to comprehensive analytics, we've got you covered.
        </p>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 w-full">
        {features.map((feature, index) => (
          <div key={index} className="group flex flex-1 flex-col gap-4 rounded-xl border border-border bg-card p-6 hover:bg-accent/50 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-primary/30">
            <div className="text-primary mb-2">
              {feature.icon}
            </div>
            <div className="flex flex-col gap-1">
              <h3 className="text-card-foreground text-lg font-semibold leading-tight">{feature.title}</h3>
              <p className="text-muted-foreground text-sm font-normal leading-relaxed">{feature.description}</p>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
};
