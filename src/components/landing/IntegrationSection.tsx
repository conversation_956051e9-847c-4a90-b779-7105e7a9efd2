export const IntegrationSection = () => {
  return (
    <section className="flex flex-col gap-8 md:gap-12 items-center">
      <div className="text-center max-w-3xl">
        <h2 className="text-foreground text-3xl md:text-4xl font-bold leading-tight tracking-tight mb-3">Developer-Friendly SDK</h2>
        <p className="text-muted-foreground text-base md:text-lg leading-relaxed text-center">
          Integrate shop credit management into your applications with our comprehensive SDK. Support for JavaScript, TypeScript, and REST API.
        </p>
      </div>
      <div className="w-full max-w-2xl mx-auto">
        <div className="bg-card rounded-xl border border-border p-6 shadow-2xl">
          <div className="flex justify-between items-center mb-4">
            <span className="text-sm text-muted-foreground">ADC Credit SDK - JavaScript</span>
            <div className="flex gap-2">
              <button className="text-muted-foreground hover:text-foreground transition-colors">
                <svg fill="currentColor" height="20" viewBox="0 0 256 256" width="20" xmlns="http://www.w3.org/2000/svg">
                  <path d="M216,32H88a8,8,0,0,0-8,8V72H40a8,8,0,0,0-8,8V216a8,8,0,0,0,8,8H168a8,8,0,0,0,8-8V184h40a8,8,0,0,0,8-8V40A8,8,0,0,0,216,32ZM160,208H48V88H160Zm48-48H176V88a8,8,0,0,0-8-8H96V48H208Z"></path>
                </svg>
              </button>
            </div>
          </div>
          <pre className="bg-muted p-4 rounded-md overflow-x-auto text-sm">
            <code className="language-javascript text-foreground">
{`// Initialize ADC Credit SDK
const { ADCCreditSDK } = require('adc-credit-sdk');
const client = new ADCCreditSDK({ apiKey: 'YOUR_API_KEY' });

// Create a shop
const shop = await client.shops.create({
  name: 'My Coffee Shop',
  description: 'Best coffee in town'
});

// Generate QR code for payment
const qrCode = await client.merchant.generateQRCode({
  shopId: shop.id,
  amount: 500, // 500 credits
  description: 'Coffee and pastry'
});

// Check customer credit balance
const balance = await client.customer.getCreditBalance({
  shopId: shop.id,
  customerId: 'customer-123'
});`}
            </code>
          </pre>
        </div>
      </div>
    </section>
  );
};
